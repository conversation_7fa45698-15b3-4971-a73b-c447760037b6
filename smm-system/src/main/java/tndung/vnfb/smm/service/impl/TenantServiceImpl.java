package tndung.vnfb.smm.service.impl;

import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import tndung.vnfb.smm.config.SubscriptionProperties;
import tndung.vnfb.smm.config.TenantContext;
import tndung.vnfb.smm.constant.Common;
import tndung.vnfb.smm.constant.enums.TenantStatus;
import tndung.vnfb.smm.constant.enums.TransactionType;
import tndung.vnfb.smm.dto.request.*;
import tndung.vnfb.smm.dto.response.GUserRes;
import tndung.vnfb.smm.dto.response.TenantHierarchyResponse;
import tndung.vnfb.smm.entity.GUser;
import tndung.vnfb.smm.entity.Tenant;
import tndung.vnfb.smm.exception.DynamicErrorCode;
import tndung.vnfb.smm.exception.IdErrorCode;
import tndung.vnfb.smm.exception.InvalidParameterException;
import tndung.vnfb.smm.repository.nontenant.TenantRepository;
import tndung.vnfb.smm.service.*;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.ZonedDateTime;
import java.util.List;
import java.util.Optional;
import java.util.UUID;

import static tndung.vnfb.smm.constant.Common.MAIN_TENANT;

@Service
@RequiredArgsConstructor
public class TenantServiceImpl implements TenantService {

    private final TenantRepository tenantRepository;
    private final SubscriptionProperties subscriptionProperties;
    private final BalanceService balanceService;
    private final AuthenticationFacade authenticationFacade;
    private final RedisTemplate<String, Object> redisTemplate;
    private final GUserService gUserService;
    private final UserTenantAccessService userTenantAccessService;

    @Autowired
    @Lazy
    private TenantService self;

    @Cacheable(value = "tenantsByDomain", key = "#domain")
    @Override
    public Optional<Tenant> findByDomain(String domain) {
        return tenantRepository.findByDomain(domain);
    }

    @Override
    @Cacheable(value = "tenantsById", key = "#id", condition = "#id != null")
    public Optional<Tenant> findByTenantId(String id) {
        if (id == null || id.trim().isEmpty()) {
            return Optional.empty();
        }
        return tenantRepository.findById(id);
    }

    @Override
    @Cacheable(value = "mainTenant")
    public Optional<Tenant> findMainTenant() {
        return tenantRepository.findByMainIsTrue();
    }

    @Override
    public List<Tenant> findAll() {
        return tenantRepository.findAll();
    }

    @Override
    public boolean isMainTenantSite() {
        return MAIN_TENANT.equals(TenantContext.getSiteTenant());
    }

    @Override
    public String getTenantId() {
        if (isMainTenantSite()) {
            return TenantContext.getCurrentTenant();
        }
        return TenantContext.getSiteTenant();
    }

    @Override
    public List<Tenant> findByStatus(TenantStatus status) {
        return tenantRepository.findByStatus(status);
    }

    @Override
    @CacheEvict(value = {"tenantsByDomain", "tenantsById", "tenantInfo"}, allEntries = true)
    public Tenant save(Tenant tenant) {
        return tenantRepository.save(tenant);
    }

    @Override
    public Tenant save(DomainReq req) {
        Tenant tenant = new Tenant();
        tenant.setDomain(req.getDomain());
        tenant.setStatus(req.getStatus() != null ? req.getStatus() : TenantStatus.New);
        tenant.setId(UUID.randomUUID().toString());
        tenant.setApiUrl(req.getApiUrl());
        tenant.setSiteUrl(req.getSiteUrl());
        tenant.setContactEmail(req.getContactEmail());
        tenant.setIsDeleted(false);
        return save(tenant);
    }

    @Override
    @Transactional
    @CacheEvict(value = {"tenantsByDomain", "tenantsById"}, allEntries = true)
    public void delete(String domain) {
        tenantRepository.deleteByDomain(domain);
    }

    @Override
    @Transactional
    @CacheEvict(value = {"tenantsByDomain", "tenantsById", "tenantInfo"}, allEntries = true)
    public void updateDiscountSystemEnabled(boolean enabled) {
        String tenantId = TenantContext.getCurrentTenant();
        Tenant tenant = tenantRepository.findById(tenantId)
                .orElseThrow(() -> new InvalidParameterException(IdErrorCode.TENANT_NOT_FOUND));

        tenant.setEnableDiscountSystem(enabled);
        tenantRepository.save(tenant);
    }

    @Override
    public List<Tenant> findSubTenants(String parentTenantId) {
        return tenantRepository.findByParentTenantIdAndIsDeletedFalseOrderByCreatedAt(parentTenantId);
    }

    @Override
    public List<Tenant> findAllParentTenants() {
        return tenantRepository.findAllParentTenants();
    }

    @Override
    public List<Tenant> findAllSubTenants() {
        return tenantRepository.findAllSubTenants();
    }

    @Override
    public Optional<Tenant> findParentTenant(String subTenantId) {
        return tenantRepository.findParentTenant(subTenantId);
    }

    @Override
    public TenantHierarchyResponse getParentTenantHierarchy(String subTenantId) {
        Optional<Tenant> parentTenant = findParentTenant(subTenantId);
        if (parentTenant.isPresent()) {
            return convertToHierarchyResponse(parentTenant.get());
        }
        throw new InvalidParameterException(IdErrorCode.TENANT_NOT_FOUND);
    }



    @Override
    public boolean canCreateSubTenant(String tenantId) {
        Boolean canCreate = tenantRepository.canCreateSubTenant(tenantId);
        return canCreate != null && canCreate;
    }




    @Override
    public boolean isSubTenant(String tenantId) {
        Boolean result = tenantRepository.isSubTenant(tenantId);
        return result != null && result;
    }

    @Override
    public TenantHierarchyResponse requestChildTenant(ChildTenantReq req) {
        Optional<Tenant> existsTenant = tenantRepository.findByDomain(req.getDomain());
        if (existsTenant.isPresent()) {
            Tenant tenant = existsTenant.get();
            if (tenant.getStatus() != TenantStatus.Reject) {
                throw new InvalidParameterException(IdErrorCode.DOMAIN_ALREADY_EXISTS);
            }
        }
        final GUser currentUser = authenticationFacade.getCurrentUser().orElseThrow(()
                -> new InvalidParameterException(IdErrorCode.USER_NOT_FOUND));
        BigDecimal baseCost = subscriptionProperties.getRenewalCost();
        // Tạo note cho transaction
        String note = String.format("Subscription fee for %d days (rate: %s/%d days)",
                30, baseCost, 30);
        balanceService.deductBalance(
                currentUser,
                baseCost,
                TransactionType.Spent,
                "SUBSCRIPTION",
                note
        );


        tenantRepository.deleteByDomain(req.getDomain());
        final Tenant newTenant = new Tenant();
        newTenant.setDomain(req.getDomain());
        newTenant.setApiUrl();
        newTenant.setParentTenantId(TenantContext.getSiteTenant());
        newTenant.setStatus(TenantStatus.Waiting);
        newTenant.setIsDeleted(false);
        newTenant.setMain(false);

        req.setEmail(currentUser.getEmail());
        req.setPhone(currentUser.getPhone());
        redisTemplate.opsForValue().set(Common.Redis.DOMAIN_REQUEST_INFO + newTenant.getId(), req );
        return convertToHierarchyResponse(tenantRepository.save(newTenant));

    }

    @Override
    public TenantHierarchyResponse approveSubTenant(String tenantId) {
        Optional<Tenant> existsTenant = tenantRepository.findById(tenantId);
        if (existsTenant.isEmpty()) {
            throw new InvalidParameterException(IdErrorCode.TENANT_NOT_FOUND);
        }
        self.deductUserBalance(subscriptionProperties.getDefaultRenewalDays());

        Tenant tenant = existsTenant.get();
        if (tenant.getStatus() != TenantStatus.Waiting) {
            throw new InvalidParameterException(IdErrorCode.TENANT_NOT_FOUND);
        }
        tenant.setStatus(TenantStatus.New);
        // create admin user:
        ChildTenantReq childTenantReq = (ChildTenantReq) redisTemplate.opsForValue()
                .get(Common.Redis.DOMAIN_REQUEST_INFO + tenantId);
        if (childTenantReq == null) {
            throw new InvalidParameterException(IdErrorCode.TENANT_NOT_FOUND);
        }

        GUserRes userRes= gUserService.create(UserReq.builder().userName(childTenantReq.getAdminUserName())
                .password(childTenantReq.getAdminPassword())
                        .email(childTenantReq.getEmail())
                        .phone(childTenantReq.getPhone())
                .build());

        userTenantAccessService.grantTenantAccess(userRes.getId(), tenantId);

        redisTemplate.delete(Common.Redis.DOMAIN_REQUEST_INFO + tenantId);
        return convertToHierarchyResponse(tenantRepository.save(tenant));

    }

    @Override
    public TenantHierarchyResponse rejectSubTenant(String tenantId) {
        Optional<Tenant> existsTenant = tenantRepository.findById(tenantId);
        if (existsTenant.isEmpty()) {
            throw new InvalidParameterException(IdErrorCode.TENANT_NOT_FOUND);
        }



        Tenant tenant = existsTenant.get();
        if (tenant.getStatus() != TenantStatus.Waiting) {
            throw new InvalidParameterException(IdErrorCode.TENANT_NOT_FOUND);
        }
        tenant.setStatus(TenantStatus.Active);
        return convertToHierarchyResponse(tenantRepository.save(tenant));
    }

    @Override
    @Transactional
    public void suspendChildTenants(String tenantId) {
        List<Tenant> childTenants = findSubTenants(TenantContext.getCurrentTenant());
        Optional<Tenant> tenantOpt = childTenants.stream()
                .filter(t -> t.getId().equals(tenantId)).findFirst();
        if (tenantOpt.isEmpty()) {
            throw new InvalidParameterException(IdErrorCode.TENANT_NOT_FOUND);
        }
        final Tenant tenant = tenantOpt.get();
        tenant.setStatus(TenantStatus.Suspended);
        tenantRepository.save(tenant);
    }

    @Override
    public void activateChildTenants(String tenantId) {
        List<Tenant> childTenants = findSubTenants(TenantContext.getCurrentTenant());
        Optional<Tenant> tenantOpt = childTenants.stream()
                .filter(t -> t.getId().equals(tenantId)).findFirst();
        if (tenantOpt.isEmpty()) {
            throw new InvalidParameterException(IdErrorCode.TENANT_NOT_FOUND);
        }
        final Tenant tenant = tenantOpt.get();
        tenant.setStatus(TenantStatus.Active);
        tenantRepository.save(tenant);
    }

    @Override
    public TenantHierarchyResponse extendChildSubscription(String tenantId, ExtendTenantSubscriptionReq request) {
        List<Tenant> childTenants = findSubTenants(TenantContext.getCurrentTenant());
        Optional<Tenant> tenantOpt = childTenants.stream()
                .filter(t -> t.getId().equals(tenantId)).findFirst();


        if (tenantOpt.isEmpty()) {
            throw new InvalidParameterException(IdErrorCode.TENANT_NOT_FOUND);
        }

        self.deductUserBalance(request.getExtensionDays());
        Tenant tenant = tenantOpt.get();
        ZonedDateTime currentEndDate = tenant.getSubscriptionEndDate();
        ZonedDateTime newEndDate;

        if (currentEndDate == null || currentEndDate.isBefore(ZonedDateTime.now())) {
            newEndDate = ZonedDateTime.now().plusDays(request.getExtensionDays());
        } else {
            newEndDate = currentEndDate.plusDays(request.getExtensionDays());
        }

        tenant.setSubscriptionEndDate(newEndDate);

        if (tenant.getStatus() == TenantStatus.Expired || tenant.getStatus() == TenantStatus.Suspended) {
            tenant.setStatus(TenantStatus.Active);
        }

        Tenant savedTenant = tenantRepository.save(tenant);
        return convertToHierarchyResponse(savedTenant);
    }

    public TenantHierarchyResponse convertToHierarchyResponse(Tenant tenant) {
        return TenantHierarchyResponse.builder()
                .id(tenant.getId())
                .domain(tenant.getDomain())
                .status(tenant.getStatus())
                .apiUrl(tenant.getApiUrl())
                .siteUrl(tenant.getSiteUrl())
                .contactEmail(tenant.getContactEmail())
                .main(tenant.getMain())
                .createdAt(tenant.getCreatedAt())
                .parentTenantId(tenant.getParentTenantId())
                .daysUntilExpiration(tenant.getDaysUntilExpiration())
                .lastRenewalDate(tenant.getLastRenewalDate())
                .subscriptionEndDate(tenant.getSubscriptionEndDate())
                .subscriptionStartDate(tenant.getSubscriptionStartDate())
                .build();
    }

    public Tenant convertToEntity(SubTenantCreateRequest request) {
        Tenant tenant = new Tenant();
        tenant.setDomain(request.getDomain());
        tenant.setApiUrl(request.getApiUrl());
        tenant.setSiteUrl(request.getSiteUrl());
        tenant.setContactEmail(request.getContactEmail());
        tenant.setStatus(request.getStatus());
        tenant.setMain(false);

        if (request.getDefaultLanguage() != null) {
            tenant.setDefaultLanguage(request.getDefaultLanguage());
        }
        if (request.getAvailableLanguages() != null) {
            tenant.setAvailableLanguages(request.getAvailableLanguages());
        }
        if (request.getAvailableCurrencies() != null) {
            tenant.setAvailableCurrencies(request.getAvailableCurrencies());
        }
        if (request.getEnableAffiliate() != null) {
            tenant.setEnableAffiliate(request.getEnableAffiliate());
        }
        if (request.getEnableDiscountSystem() != null) {
            tenant.setEnableDiscountSystem(request.getEnableDiscountSystem());
        }
        if (request.getDesignSettings() != null) {
            tenant.setDesignSettings(request.getDesignSettings());
        }
        if (request.getDecimalPlaces() != null) {
            tenant.setDecimalPlaces(request.getDecimalPlaces());
        }
        if (request.getConnectionSettings() != null) {
            tenant.setConnectionSettings(request.getConnectionSettings());
        }
        if (request.getAverageTimeSettings() != null) {
            tenant.setAverageTimeSettings(request.getAverageTimeSettings());
        }

        return tenant;
    }

    /**
     * Trừ tiền từ user hiện tại theo cài đặt giá tiền cho số ngày được chỉ định
     * Tính lại giá theo tỷ lệ với 30 ngày mặc định
     *
     * @param days số ngày cần tính phí
     * @return balance mới sau khi trừ tiền
     * @throws InvalidParameterException nếu user không đủ tiền
     */
    @Transactional
    public void deductUserBalance(int days) {
        if (days <= 0) {
            throw new InvalidParameterException(new DynamicErrorCode(4300, "Number of days must be greater than 0", 400));
        }

        // Lấy user hiện tại
        var currentUser = authenticationFacade.getCurrentUser();

        if (currentUser.isEmpty()) {
            throw new InvalidParameterException(IdErrorCode.USER_NOT_FOUND);
        }

        // Tính giá theo tỷ lệ với 30 ngày mặc định
        BigDecimal baseCost = subscriptionProperties.getRenewalCost();
        int defaultDays = subscriptionProperties.getDefaultRenewalDays();

        BigDecimal calculatedCost = baseCost
                .multiply(BigDecimal.valueOf(days))
                .divide(BigDecimal.valueOf(defaultDays), 2, RoundingMode.HALF_UP);

        // Tạo note cho transaction
        String note = String.format("Subscription fee for %d days (rate: %s/%d days)",
                days, baseCost, defaultDays);

        // Sử dụng BalanceService để trừ tiền
        var updatedUser = balanceService.deductBalance(
                currentUser.get(),
                calculatedCost,
                TransactionType.Spent,
                "SUBSCRIPTION",
                note
        );

    }
}