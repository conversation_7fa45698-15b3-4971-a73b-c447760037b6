package tndung.vnfb.smm.controller;

import lombok.RequiredArgsConstructor;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;
import tndung.vnfb.smm.anotation.TenantCheck;
import tndung.vnfb.smm.dto.ApiResponseEntity;
import tndung.vnfb.smm.dto.request.ChildTenantReq;
import tndung.vnfb.smm.dto.request.ExtendTenantSubscriptionReq;
import tndung.vnfb.smm.dto.response.TenantHierarchyResponse;
import tndung.vnfb.smm.entity.Tenant;
import tndung.vnfb.smm.service.TenantService;
import tndung.vnfb.smm.config.TenantContext;

import javax.validation.Valid;
import java.util.List;
import java.util.stream.Collectors;

/**
 * Controller for managing sub-tenant hierarchy (Single level: Parent -> Child only)
 */
@RestController
@RequestMapping("/v1/sub-tenants")
@RequiredArgsConstructor
@PreAuthorize("hasAnyRole('ROLE_PANEL', 'ROLE_CHILD_PANEL')")
public class SubTenantController {

    private final TenantService tenantService;

    /**
     * Get all parent tenants (tenants that can have sub-tenants)
     */
    @GetMapping("/parents")
    @TenantCheck
    public ResponseEntity<List<TenantHierarchyResponse>> getParentTenants() {
        List<Tenant> parentTenants = tenantService.findAllParentTenants();
        List<TenantHierarchyResponse> response = parentTenants.stream()
                .map(tenantService::convertToHierarchyResponse)
                .collect(Collectors.toList());
        return ResponseEntity.ok(response);
    }


    /**
     * Get all parent tenants (tenants that can have sub-tenants)
     */
    @PostMapping("/request")
    @TenantCheck
    public ResponseEntity<TenantHierarchyResponse> requestSubTenant(@RequestBody @Valid ChildTenantReq childTenantReq) {
        return ResponseEntity.ok(tenantService.requestChildTenant(childTenantReq));
    }


    /**
     * Get all parent tenants (tenants that can have sub-tenants)
     */
    @PutMapping("/{tenantId}/approve")
    @TenantCheck
    public ResponseEntity<TenantHierarchyResponse> approveSubTenant(@PathVariable String tenantId) {
       return ResponseEntity.ok(tenantService.approveSubTenant(tenantId));
    }

    /**
     * Get all parent tenants (tenants that can have sub-tenants)
     */
    @PutMapping("/{tenantId}/reject")
    @TenantCheck
    public ResponseEntity<TenantHierarchyResponse> rejectSubTenant(@PathVariable String tenantId) {
        return ResponseEntity.ok(tenantService.rejectSubTenant(tenantId));
    }

    /**
     * Get all sub-tenants (child tenants)
     */
    @GetMapping("/children")
    @TenantCheck
    public ResponseEntity<List<TenantHierarchyResponse>> getAllSubTenants() {
        List<Tenant> subTenants = tenantService.findAllSubTenants();
        List<TenantHierarchyResponse> response = subTenants.stream()
                .map(tenantService::convertToHierarchyResponse)
                .collect(Collectors.toList());
        return ResponseEntity.ok(response);
    }

    /**
     * Get sub-tenants of a parent tenant
     */
    @GetMapping("/{parentTenantId}/children")
    @TenantCheck
    public ApiResponseEntity<List<TenantHierarchyResponse>> getSubTenants(@PathVariable String parentTenantId) {
        List<Tenant> subTenants = tenantService.findSubTenants(parentTenantId);
        List<TenantHierarchyResponse> response = subTenants.stream()
                .map(tenantService::convertToHierarchyResponse)
                .collect(Collectors.toList());
        return ApiResponseEntity.success(response);
    }

    /**
     * Get sub-tenants of current tenant
     */
    @GetMapping("/current/children")
    @TenantCheck
    public ApiResponseEntity<List<TenantHierarchyResponse>> getCurrentTenantSubTenants() {
        String currentTenantId = TenantContext.getCurrentTenant();

        List<Tenant> subTenants = tenantService.findSubTenants(currentTenantId);
        List<TenantHierarchyResponse> response = subTenants.stream()
                .map(tenantService::convertToHierarchyResponse)
                .collect(Collectors.toList());
        return ApiResponseEntity.success(response);
    }

    /**
     * Check if tenant is a sub-tenant
     */
    @PutMapping("/current/{tenantId}/suspend")
    @TenantCheck
    public ApiResponseEntity<String> suspendTenant(@PathVariable String tenantId) {
        tenantService.suspendChildTenants(tenantId);
        return ApiResponseEntity.success();
    }

    /**
     * Check if tenant is a sub-tenant
     */
    @PutMapping("/current/{tenantId}/activation")
    @TenantCheck
    public ApiResponseEntity<String> activateTenant(@PathVariable String tenantId) {
        tenantService.activateChildTenants(tenantId);
        return ApiResponseEntity.success();
    }


    /**
     * Check if tenant is a sub-tenant
     */
    @PutMapping("/current/{tenantId}/extend-subscription")
    @TenantCheck
    public ApiResponseEntity<TenantHierarchyResponse> extendTenantSubscription(@PathVariable String tenantId,
                                                                               @RequestBody @Valid ExtendTenantSubscriptionReq request
    ) {

        return ApiResponseEntity.success(tenantService.extendChildSubscription(tenantId, request));
    }


    /**
     * Get parent tenant of a sub-tenant
     */
    @GetMapping("/{subTenantId}/parent")
    @TenantCheck
    public ApiResponseEntity<TenantHierarchyResponse> getParentTenant(@PathVariable String subTenantId) {
        return ApiResponseEntity.success(tenantService.getParentTenantHierarchy(subTenantId));
    }


    /**
     * Check if tenant is a sub-tenant
     */
    @GetMapping("/{tenantId}/is-sub-tenant")
    @TenantCheck
    public ApiResponseEntity<Boolean> isSubTenant(@PathVariable String tenantId) {
        boolean isSubTenant = tenantService.isSubTenant(tenantId);
        return ApiResponseEntity.success(isSubTenant);
    }


}
