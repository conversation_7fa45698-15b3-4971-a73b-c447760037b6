import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Observable } from 'rxjs';
import { ConfigService } from './config.service';


export interface SubTenantCreateRequest {
    domain: string;
    admin_name: string;
    admin_password: string;

}

export interface SubTenantResponse {
  id: string;
  domain: string;
  status: string;
  parentTenantId: string;
  createdAt: string;
  // Add other response properties as needed
}



@Injectable({
  providedIn: 'root'
})
export class SubTenantService {

  constructor(
    private http: HttpClient,
    private configService: ConfigService
  ) {}

  /**
   * Create a new sub-tenant
   */
  createSubTenant(request: SubTenantCreateRequest): Observable<SubTenantResponse> {
    return this.http.post<SubTenantResponse>(
      `${this.configService.apiUrl}/v1/sub-tenants/request`,
      request
    );
  }

  /**
   * Get all sub-tenants for current tenant
   */
  getSubTenants(): Observable<SubTenantResponse[]> {
    return this.http.get<SubTenantResponse[]>(
      `${this.configService.apiUrl}/api/admin/sub-tenants`
    );
  }


}