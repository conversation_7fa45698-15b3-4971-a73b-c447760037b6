import { Component, OnInit, OnD<PERSON>roy } from '@angular/core';
import { Subject } from 'rxjs';
import { takeUntil } from 'rxjs/operators';
import { InfoChildPanelComponent } from "./info-child-panel/info-child-panel.component";
import { FaqChildPanelComponent } from "./faq-child-panel/faq-child-panel.component";
import { TranslateModule } from '@ngx-translate/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { IconsModule } from '../../icons/icons.module';
import { TenantService } from '../../core/services/tenant.service';
import { ToastService } from '../../core/services/toast.service';
import { SubTenantCreateRequest, SubTenantService } from '../../core/services/sub-tenant.service';


interface FormErrors {
  domainName?: string;
  adminName?: string;
  adminPassword?: string;
  confirmPassword?: string;
}

@Component({
  selector: 'app-new-child-panel',
  standalone: true,
  imports: [
    InfoChildPanelComponent,
    FaqChildPanelComponent,
    TranslateModule,
    CommonModule,
    FormsModule,
    IconsModule
  ],
  templateUrl: './new-child-panel.component.html',
  styleUrl: './new-child-panel.component.css'
})
export class NewChildPanelComponent implements OnInit, OnDestroy {
  private destroy$ = new Subject<void>();

  // Form fields
  domainName: string = '';
  adminName: string = '';
  adminPassword: string = '';
  confirmPassword: string = '';

  // Price display
  priceDisplay: string = '$7.00';

  // Form validation
  formErrors: FormErrors = {};

  // Password visibility
  passwordVisible: boolean = false;
  confirmPasswordVisible: boolean = false;

  // Loading state
  isSubmitting: boolean = false;

  // Current tenant
  currentTenantId: string | null = null;

  constructor(
    private tenantService: TenantService,
    private subTenantService: SubTenantService,
    private toastService: ToastService
  ) {}

  ngOnInit(): void {
    this.initializeForm();
    this.subscribeToCurrentTenant();
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }

  private initializeForm(): void {
    this.domainName = '';
    this.adminName = '';
    this.adminPassword = '';
    this.confirmPassword = '';
    this.formErrors = {};
  }

  private subscribeToCurrentTenant(): void {
    this.tenantService.currentTenant$
      .pipe(takeUntil(this.destroy$))
      .subscribe(tenant => {
        if (tenant) {
          this.currentTenantId = this.extractTenantId(tenant);
        }
      });
  }

  private extractTenantId(tenant: any): string {
    return typeof tenant === 'string' ? tenant : (tenant?.id || tenant);
  }

  togglePasswordVisibility(field: string): void {
    if (field === 'password') {
      this.passwordVisible = !this.passwordVisible;
      this.updatePasswordFieldType('password', this.passwordVisible);
    } else if (field === 'confirm') {
      this.confirmPasswordVisible = !this.confirmPasswordVisible;
      this.updatePasswordFieldType('confirm', this.confirmPasswordVisible);
    }
  }

  private updatePasswordFieldType(fieldType: string, visible: boolean): void {
    const selector = fieldType === 'password' 
      ? 'input[type="password"][placeholder="Enter password"]'
      : 'input[type="password"][placeholder="Confirm password"]';
    
    const inputField = document.querySelector(selector) as HTMLInputElement;
    if (inputField) {
      inputField.type = visible ? 'text' : 'password';
    }
  }

  validateForm(): boolean {
    this.formErrors = {};
    let isValid = true;

    // Validate domain name
    if (!this.domainName?.trim()) {
      this.formErrors.domainName = 'Domain name is required';
      isValid = false;
    }

    // Validate admin name
    if (!this.adminName?.trim()) {
      this.formErrors.adminName = 'Admin name is required';
      isValid = false;
    }

    // Validate admin password
    if (!this.adminPassword?.trim()) {
      this.formErrors.adminPassword = 'Password is required';
      isValid = false;
    } else if (this.adminPassword.length < 6) {
      this.formErrors.adminPassword = 'Password must be at least 6 characters';
      isValid = false;
    }

    // Validate confirm password
    if (this.adminPassword !== this.confirmPassword) {
      this.formErrors.confirmPassword = 'Passwords do not match';
      isValid = false;
    }

    return isValid;
  }

  submitForm(): void {
    if (!this.validateForm()) {
      console.log('Form validation failed', this.formErrors);
      return;
    }

    this.isSubmitting = true;

   
    // Create sub-tenant request
    const request: SubTenantCreateRequest = {
      domain: this.domainName,
      admin_name: this.adminName,
      admin_password: this.adminPassword
    };

    // Call API to create sub-tenant
    this.subTenantService.createSubTenant(request)
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (response) => {
          console.log('Sub-tenant created successfully', response);
          this.toastService.showSuccess('Child panel created successfully! Status: WAITING');
          this.resetForm();
        },
        error: (error) => {
          console.error('Error creating sub-tenant:', error);
          const errorMessage =  error?.message || 'Unknown error occurred';
          this.toastService.showError(`Failed to create child panel: ${errorMessage}`);
        },
        complete: () => {
          this.isSubmitting = false;
        }
      });
  }


  private resetForm(): void {
    this.domainName = '';
    this.adminName = '';
    this.adminPassword = '';
    this.confirmPassword = '';
    this.formErrors = {};
    this.passwordVisible = false;
    this.confirmPasswordVisible = false;
  }
}